{
  "include": [
    "**/*",
    "**/.server/**/*",
    "**/.client/**/*",
    ".react-router/types/**/*"
  ],
  "compilerOptions": {
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ES2022"
    ],
    "types": [
      "node",
      "vite/client"
    ],
    "target": "ES2022",
    "module": "ES2022",
    "moduleResolution": "bundler",
    "jsx": "react-jsx",
    "rootDirs": [
      ".",
      "./.react-router/types"
    ],
    "baseUrl": ".",
    "paths": {
      "~/*": [
        "./app/*"
      ]
    },
    "esModuleInterop": false,
    "verbatimModuleSyntax": true,
    "noEmit": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": true,
  }
}