import React from 'react';
import type { Route } from './+types/initiatives';
import {
  Text,
  Card,
  CardHeader,
  CardPreview,
  makeStyles,
  tokens,
} from '@fluentui/react-components';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: tokens.spacingVerticalL,
    maxWidth: '1200px',
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
  },
  card: {
    width: '100%',
  },
  cardContent: {
    padding: tokens.spacingHorizontalL,
  },
});

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'Initiatives - Vast' },
    { name: 'description', content: 'Track and manage your strategic initiatives' },
  ];
}

export default function Initiatives() {
  const styles = useStyles();

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Text size={800} weight="semibold">
          Initiatives
        </Text>
        <Text size={400} style={{ marginTop: tokens.spacingVerticalXS }}>
          Track and manage your strategic initiatives
        </Text>
      </div>

      <Card className={styles.card}>
        <CardHeader>
          <Text size={500} weight="semibold">
            Strategic Overview
          </Text>
        </CardHeader>
        <CardPreview>
          <div className={styles.cardContent}>
            <Text size={300}>
              Initiatives help you organize and track high-level strategic goals. 
              Use this section to define key objectives, assign resources, and 
              monitor progress toward your organization's most important outcomes.
            </Text>
          </div>
        </CardPreview>
      </Card>

      <Card className={styles.card}>
        <CardHeader>
          <Text size={500} weight="semibold">
            Active Initiatives
          </Text>
        </CardHeader>
        <CardPreview>
          <div className={styles.cardContent}>
            <Text size={300}>
              No initiatives have been created yet. Start by defining your first 
              strategic initiative to begin tracking progress toward your key objectives.
            </Text>
          </div>
        </CardPreview>
      </Card>
    </div>
  );
}
