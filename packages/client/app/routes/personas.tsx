import React from 'react';
import type { Route } from './+types/personas';
import {
  Text,
  Card,
  CardHeader,
  CardPreview,
  makeStyles,
  tokens,
} from '@fluentui/react-components';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: tokens.spacingVerticalL,
    maxWidth: '1200px',
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
  },
  card: {
    width: '100%',
  },
  cardContent: {
    padding: tokens.spacingHorizontalL,
  },
});

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'Personas - Vast' },
    { name: 'description', content: 'Define and manage user personas' },
  ];
}

export default function Personas() {
  const styles = useStyles();

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Text size={800} weight="semibold">
          Personas
        </Text>
        <Text size={400} style={{ marginTop: tokens.spacingVerticalXS }}>
          Define and manage user personas for better product understanding
        </Text>
      </div>

      <Card className={styles.card}>
        <CardHeader>
          <Text size={500} weight="semibold">
            User-Centered Design
          </Text>
        </CardHeader>
        <CardPreview>
          <div className={styles.cardContent}>
            <Text size={300}>
              Personas represent your target users and help guide product decisions. 
              Create detailed user profiles that capture demographics, behaviors, 
              goals, and pain points to ensure your product meets real user needs.
            </Text>
          </div>
        </CardPreview>
      </Card>

      <Card className={styles.card}>
        <CardHeader>
          <Text size={500} weight="semibold">
            Your Personas
          </Text>
        </CardHeader>
        <CardPreview>
          <div className={styles.cardContent}>
            <Text size={300}>
              No personas have been created yet. Start by defining your first user 
              persona to better understand and design for your target audience.
            </Text>
          </div>
        </CardPreview>
      </Card>
    </div>
  );
}
