import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import type { Route } from './+types/index';
import { useAuth } from '../features/auth/AuthContext';

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'Vast' },
    { name: 'description', content: 'Welcome to Vast' },
  ];
}

export default function Index() {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated) {
        // User is authenticated, redirect to requirements
        navigate('/requirements', { replace: true });
      } else {
        // User is not authenticated, redirect to login
        navigate('/login', { replace: true });
      }
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Show nothing while redirecting
  return null;
}
