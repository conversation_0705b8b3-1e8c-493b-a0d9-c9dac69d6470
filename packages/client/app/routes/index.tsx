import { useEffect } from "react";
import { useNavigate } from "react-router";
import type { Route } from "./+types/index";
import { useAuth } from "../features/auth/AuthContext";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Vast" },
    { name: "description", content: "Welcome to Vast" },
  ];
}

export default function Index() {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();

  console.log("Index route rendered", { isAuthenticated, isLoading });

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated) {
        // User is authenticated, redirect to requirements
        console.log("Index: Redirecting to requirements - authenticated");
        navigate("/requirements", { replace: true });
      } else {
        // User is not authenticated, redirect to login
        console.log("Index: Redirecting to login - not authenticated");
        navigate("/login", { replace: true });
      }
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <div>Loading...</div>
      </div>
    );
  }

  // Show nothing while redirecting
  return null;
}
