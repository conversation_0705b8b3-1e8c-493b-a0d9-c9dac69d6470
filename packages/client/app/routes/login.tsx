import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import type { Route } from './+types/login';
import { LoginForm } from '../features/auth/components/LoginForm';
import { useAuth } from '../features/auth/AuthContext';

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'Login - Vast' },
    { name: 'description', content: 'Sign in to your Vast account' },
  ];
}

export default function Login() {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  const handleLoginSuccess = () => {
    navigate('/', { replace: true });
  };

  const handleRegisterClick = () => {
    navigate('/register');
  };

  if (isAuthenticated) {
    return null; // Will redirect via useEffect
  }

  return (
    <LoginForm
      onSuccess={handleLoginSuccess}
      onRegisterClick={handleRegisterClick}
    />
  );
}
