import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import type { Route } from './+types/register';
import { RegisterForm } from '../features/auth/components/RegisterForm';
import { useAuth } from '../features/auth/AuthContext';

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'Register - Vast' },
    { name: 'description', content: 'Create your Vast account' },
  ];
}

export default function Register() {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  const handleRegisterSuccess = () => {
    navigate('/', { replace: true });
  };

  const handleLoginClick = () => {
    navigate('/login');
  };

  if (isAuthenticated) {
    return null; // Will redirect via useEffect
  }

  return (
    <RegisterForm
      onSuccess={handleRegisterSuccess}
      onLoginClick={handleLoginClick}
    />
  );
}
