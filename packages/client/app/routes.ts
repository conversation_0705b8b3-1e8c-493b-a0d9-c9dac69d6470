import {
  type RouteConfig,
  index,
  route,
  layout,
} from "@react-router/dev/routes";

export default [
  // Index route - handles redirects
  index("./routes/index.tsx"),

  // Auth routes (no layout)
  route("login", "./routes/login.tsx"),
  route("register", "./routes/register.tsx"),

  layout("./layouts/AppLayout.tsx", [
    // Main app routes (with layout)
    route("requirements", "./routes/requirements.tsx"),
    route("initiatives", "./routes/initiatives.tsx"),
    route("personas", "./routes/personas.tsx"),
  ]),
] satisfies RouteConfig;
