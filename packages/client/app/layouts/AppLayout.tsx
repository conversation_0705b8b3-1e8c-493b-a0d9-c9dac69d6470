import { useEffect } from "react";
import { useNavigate, Outlet } from "react-router";
import { makeStyles, tokens } from "@fluentui/react-components";
import { useAuth } from "../features/auth/AuthContext";
import { Header } from "../features/header/Header";
import { Sidebar } from "../features/sidebar/Sidebar";

const useStyles = makeStyles({
  layout: {
    display: "flex",
    flexDirection: "column",
    height: "100vh",
    overflow: "hidden",
  },
  main: {
    display: "flex",
    flex: 1,
    overflow: "hidden",
  },
  content: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    overflow: "hidden",
  },
  contentArea: {
    flex: 1,
    padding: tokens.spacingHorizontalL,
    backgroundColor: tokens.colorNeutralBackground1,
    overflow: "auto",
  },
});

export function AppLayout() {
  const styles = useStyles();
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();

  console.log("AppLayout rendered", { isAuthenticated, isLoading });

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      console.log("AppLayout: Redirecting to login - not authenticated");
      navigate("/login", { replace: true });
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <div>Loading...</div>
      </div>
    );
  }

  // Don't render layout if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className={styles.layout}>
      <Header />
      <div className={styles.main}>
        <Sidebar />
        <div className={styles.content}>
          <main className={styles.contentArea}>
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  );
}
