import React, { useState } from "react";
import {
  <PERSON>,
  CardHeader,
  Text,
  Field,
  Input,
  <PERSON><PERSON>,
  Link,
  Spinner,
  makeStyles,
  tokens,
} from "@fluentui/react-components";
import { useAuth } from "../AuthContext";

const useStyles = makeStyles({
  container: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    minHeight: "100vh",
    backgroundColor: tokens.colorNeutralBackground2,
    padding: tokens.spacingHorizontalM,
  },
  card: {
    width: "100%",
    maxWidth: "400px",
  },
  form: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalM,
    padding: tokens.spacingHorizontalL,
  },
  header: {
    textAlign: "center",
    marginBottom: tokens.spacingVerticalL,
  },
  submitButton: {
    marginTop: tokens.spacingVerticalM,
  },
  footer: {
    textAlign: "center",
    marginTop: tokens.spacingVerticalM,
  },
  errorText: {
    color: tokens.colorPaletteRedForeground1,
    fontSize: tokens.fontSizeBase200,
  },
});

interface LoginFormProps {
  onSuccess?: () => void;
  onRegisterClick?: () => void;
}

export function LoginForm({ onSuccess, onRegisterClick }: LoginFormProps) {
  const styles = useStyles();
  const { login, isLoading } = useAuth();
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("password123");
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!email || !password) {
      setError("Please fill in all fields");
      return;
    }

    try {
      const success = await login(email, password);
      if (success) {
        onSuccess?.();
      } else {
        setError("Invalid email or password");
      }
    } catch (err) {
      setError("An error occurred. Please try again.");
    }
  };

  return (
    <div className={styles.container}>
      <Card className={styles.card}>
        <CardHeader>
          <div className={styles.header}>
            <Text size={600} weight="semibold">
              Welcome to Vast
            </Text>
            <Text size={300} style={{ marginTop: tokens.spacingVerticalXS }}>
              Sign in to your account
            </Text>
          </div>
        </CardHeader>
        <form onSubmit={handleSubmit} className={styles.form}>
          <Field label="Email" required>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              disabled={isLoading}
            />
          </Field>

          <Field label="Password" required>
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              disabled={isLoading}
            />
          </Field>

          {error && <Text className={styles.errorText}>{error}</Text>}

          <Button
            type="submit"
            appearance="primary"
            size="large"
            disabled={isLoading}
            className={styles.submitButton}
          >
            {isLoading ? (
              <>
                <Spinner size="tiny" />
                Signing in...
              </>
            ) : (
              "Sign In"
            )}
          </Button>

          <div className={styles.footer}>
            <Text size={200}>
              Don't have an account?{" "}
              <Link onClick={onRegisterClick}>Sign up</Link>
            </Text>
          </div>
        </form>
      </Card>
    </div>
  );
}
